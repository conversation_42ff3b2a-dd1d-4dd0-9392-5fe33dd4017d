# Graphviz Diagram Extension

This extension provides support for Graphviz DOT format diagrams in the AutoCRUD project, implementing both LanguageSketchProvider and IntelliJ's Diagram interfaces.

## Features

### 1. LanguageSketchProvider Support
- **GraphvizSketchProvider**: Supports `graphviz`, `dot`, and `gv` language identifiers
- **Real-time visualization**: Parses and renders Graphviz content as you type
- **Error handling**: Shows meaningful error messages for invalid DOT syntax

### 2. IntelliJ Diagram Integration
- **GraphvizDiagramProvider**: Full integration with IntelliJ's diagram framework
- **File support**: Automatically recognizes `.dot`, `.gv`, and `.graphviz` files
- **Interactive diagrams**: Navigate and interact with diagram elements

### 3. Model Classes
- **GraphvizGraph**: Represents complete graph structures with support for subgraphs
- **GraphvizNode**: Implements `DiagramNode<GraphvizNode>` interface
- **GraphvizEdge**: Implements `DiagramEdge<GraphvizNode>` interface

### 4. Build<PERSON> Pattern
- **GraphvizDiagramBuilder**: Programmatic graph construction
- **Fluent API**: Easy-to-use builder methods
- **DOT import**: Create builders from existing DOT content

## Usage Examples

### Using the Builder
```kotlin
val graph = GraphvizDiagramBuilder
    .digraph("MyGraph")
    .addNode("A", "Start Node")
    .addNode("B", "End Node") 
    .addEdge("A", "B", "flow")
    .attribute("rankdir", "LR")
    .build()

println(graph.toDotString())
```

### DOT Format Support
```dot
digraph G {
    rankdir=LR;
    
    A [label="Start", shape=ellipse, color=green];
    B [label="Process", shape=box];
    C [label="Decision", shape=diamond];
    D [label="End", shape=ellipse, color=red];
    
    A -> B [label="begin"];
    B -> C [label="check"];
    C -> D [label="yes"];
    C -> B [label="no"];
}
```

### LanguageSketchProvider Integration
The extension automatically registers with the sketch system to handle:
- `graphviz` language blocks
- `dot` language blocks  
- `gv` language blocks

## Architecture

### Core Components

1. **Model Layer**
   - `GraphvizGraph`: Main graph container
   - `GraphvizNode`: Node implementation with DiagramNode interface
   - `GraphvizEdge`: Edge implementation with DiagramEdge interface

2. **Parser Layer**
   - `GraphvizDotParser`: Parses DOT format using graphviz-java library
   - Converts between graphviz-java models and our domain models

3. **UI Layer**
   - `GraphvizVisualizationPanel`: Renders graphs as images
   - `GraphvizSketch`: Integrates with sketch system
   - `GraphvizDiagramProvider`: IntelliJ diagram integration

4. **Data Layer**
   - `GraphvizDiagramDataModel`: Manages diagram data for IntelliJ
   - `GraphvizDiagramBuilder`: Programmatic graph construction

### Dependencies

- **IntelliJ Diagram Plugin**: `com.intellij.diagram`
- **Graphviz Java Library**: For DOT parsing and rendering (removed from dependencies)
- **AutoCRUD Core**: For LanguageSketchProvider integration

## File Structure

```
exts/ext-diagram/src/main/kotlin/cc/unitmesh/diagram/
├── StructureLooker.kt                    # Structure analyzer
└── graphviz/
    ├── GraphvizSketchProvider.kt         # LanguageSketchProvider impl
    ├── GraphvizDiagramProvider.kt        # DiagramProvider impl  
    ├── GraphvizDiagramDataModel.kt       # DiagramDataModel impl
    ├── GraphvizDiagramBuilder.kt         # Builder pattern
    ├── model/
    │   ├── GraphvizGraph.kt              # Graph model
    │   ├── GraphvizNode.kt               # Node model (DiagramNode)
    │   └── GraphvizEdge.kt               # Edge model (DiagramEdge)
    ├── parser/
    │   └── GraphvizDotParser.kt          # DOT format parser
    └── ui/
        └── GraphvizVisualizationPanel.kt # Visualization component
```

## Configuration

The extension is registered in `cc.unitmesh.diagram.xml`:

```xml
<extensions defaultExtensionNs="cc.unitmesh">
    <langSketchProvider implementation="cc.unitmesh.diagram.graphviz.GraphvizSketchProvider"/>
</extensions>

<extensions defaultExtensionNs="com.intellij">
    <diagram.Provider implementation="cc.unitmesh.diagram.graphviz.GraphvizDiagramProvider"/>
</extensions>
```

## Testing

Run tests with:
```bash
./gradlew :exts:ext-diagram:test
```

Test files include:
- `GraphvizTest.kt`: Unit tests for core functionality
- `example.dot`: Sample DOT file for testing

## Future Enhancements

1. **Enhanced Visualization**: Better rendering options and themes
2. **Interactive Editing**: Direct manipulation of diagram elements
3. **Export Options**: Support for various output formats
4. **Validation**: Real-time DOT syntax validation
5. **Code Generation**: Generate code from diagram structures
