package cc.unitmesh.diagram.graphviz

import cc.unitmesh.diagram.graphviz.model.GraphvizNode
import com.intellij.diagram.DiagramNode
import com.intellij.openapi.util.Key
import javax.swing.Icon

/**
 * DiagramNode wrapper for GraphvizNode
 */
class GraphvizDiagramNode(
    private val graphvizNode: GraphvizNode
) : DiagramNode<GraphvizNode> {
    
    private val userData = mutableMapOf<Key<*>, Any?>()
    
    override fun getIdentifyingElement(): GraphvizNode = graphvizNode
    
    override fun getTooltip(): String? = graphvizNode.getDisplayLabel()
    
    override fun getIcon(): Icon? = null // TODO: Add appropriate icon based on node shape
    
    override fun <T : Any?> getUserData(key: Key<T>): T? {
        @Suppress("UNCHECKED_CAST")
        return userData[key] as? T
    }
    
    override fun <T : Any?> putUserData(key: Key<T>, value: T?) {
        if (value == null) {
            userData.remove(key)
        } else {
            userData[key] = value
        }
    }
    
    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (other !is GraphvizDiagramNode) return false
        return graphvizNode == other.graphvizNode
    }
    
    override fun hashCode(): Int {
        return graphvizNode.hashCode()
    }
    
    override fun toString(): String {
        return "GraphvizDiagramNode(${graphvizNode.id})"
    }
}
