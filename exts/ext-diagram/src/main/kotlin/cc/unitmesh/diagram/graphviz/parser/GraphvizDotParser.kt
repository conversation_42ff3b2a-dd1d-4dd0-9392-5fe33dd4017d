package cc.unitmesh.diagram.graphviz.parser

import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import cc.unitmesh.diagram.graphviz.model.GraphvizNode
import cc.unitmesh.diagram.graphviz.model.GraphvizEdge
import guru.nidi.graphviz.parse.Parser
import guru.nidi.graphviz.model.MutableGraph
import guru.nidi.graphviz.model.MutableNode
import guru.nidi.graphviz.model.Link


/**
 * Parser for Graphviz DOT format files
 * Uses the graphviz-java library to parse DOT content and convert to our model
 */
class GraphvizDotParser {
    
    companion object {
        /**
         * Parse DOT format content and return a GraphvizGraph model
         */
        fun parse(dotContent: String): GraphvizGraph? {
            return try {
                val mutableGraph = Parser().read(dotContent)
                convertToGraphvizGraph(mutableGraph)
            } catch (e: Exception) {
                // Log error and return null if parsing fails
                null
            }
        }
        
        /**
         * Convert graphviz-java MutableGraph to our GraphvizGraph model
         */
        private fun convertToGraphvizGraph(mutableGraph: MutableGraph): GraphvizGraph {
            val graphType = when {
                mutableGraph.isDirected -> GraphvizGraph.GraphType.DIGRAPH
                else -> GraphvizGraph.GraphType.GRAPH
            }
            
            val nodes = mutableListOf<GraphvizNode>()
            val edges = mutableListOf<GraphvizEdge>()
            
            // Extract nodes and their edges
            mutableGraph.nodes().forEach { node ->
                nodes.add(convertToGraphvizNode(node))
                
                // Extract edges from this node
                node.links().forEach { link ->
                    edges.add(convertToGraphvizEdge(node, link, mutableGraph.isDirected))
                }
            }
            
            return GraphvizGraph(
                name = mutableGraph.name().toString(),
                type = graphType,
                nodes = nodes.distinctBy { it.id },
                edges = edges,
                attributes = extractAttributes(mutableGraph.graphAttrs())
            )
        }
        
        /**
         * Convert MutableNode to GraphvizNode
         */
        private fun convertToGraphvizNode(node: MutableNode): GraphvizNode {
            val attributes = extractAttributes(node.attrs())
            val label = attributes["label"] ?: node.name().toString()
            
            return GraphvizNode(
                id = node.name().toString(),
                label = label,
                attributes = attributes
            )
        }
        
        /**
         * Convert Link to GraphvizEdge
         */
        private fun convertToGraphvizEdge(fromNode: MutableNode, link: Link, isDirected: Boolean): GraphvizEdge {
            val attributes = extractAttributes(link.attrs())
            val label = attributes["label"]
            
            return GraphvizEdge(
                from = fromNode.name().toString(),
                to = link.to().name().toString(),
                label = label,
                attributes = attributes,
                isDirected = isDirected
            )
        }
        
        /**
         * Extract attributes from graphviz-java attributes
         */
        private fun extractAttributes(attrs: Any?): Map<String, String> {
            // This is a simplified implementation
            // The actual implementation would depend on the specific structure
            // of the graphviz-java library's attribute system
            return emptyMap()
        }
        
        /**
         * Validate if the content is a valid DOT format
         */
        fun isValidDotContent(content: String): Boolean {
            return try {
                Parser().read(content)
                true
            } catch (e: Exception) {
                false
            }
        }
    }
}
