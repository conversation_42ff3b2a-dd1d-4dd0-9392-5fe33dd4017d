package cc.unitmesh.diagram.graphviz.parser

import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import cc.unitmesh.diagram.graphviz.model.GraphvizNode
import cc.unitmesh.diagram.graphviz.model.GraphvizEdge

/**
 * Parser for Graphviz DOT format files
 * Simple implementation without external dependencies
 */
class GraphvizDotParser {
    
    companion object {
        /**
         * Parse DOT format content and return a GraphvizGraph model
         */
        fun parse(dotContent: String): GraphvizGraph? {
            return try {
                val parser = GraphvizDotParser()
                parser.parseInternal(dotContent.trim())
            } catch (e: Exception) {
                // Log error and return null if parsing fails
                null
            }
        }
        
        /**
         * Validate if the content is a valid DOT format
         */
        fun isValidDotContent(content: String): Bo<PERSON>an {
            return try {
                parse(content) != null
            } catch (e: Exception) {
                false
            }
        }
    }
    
    private fun parseInternal(content: String): GraphvizGraph? {
        val lines = content.lines().map { it.trim() }.filter { it.isNotEmpty() && !it.startsWith("//") }
        if (lines.isEmpty()) return null
        
        val firstLine = lines[0]
        val graphInfo = parseGraphDeclaration(firstLine) ?: return null
        
        val nodes = mutableListOf<GraphvizNode>()
        val edges = mutableListOf<GraphvizEdge>()
        val attributes = mutableMapOf<String, String>()
        
        var i = 1
        while (i < lines.size) {
            val line = lines[i].trim()
            if (line == "}") break
            
            when {
                line.contains("->") || line.contains("--") -> {
                    parseEdge(line, graphInfo.isDirected)?.let { edges.add(it) }
                }
                line.contains("[") && line.contains("]") -> {
                    parseNode(line)?.let { nodes.add(it) }
                }
                line.contains("=") && !line.contains("[") -> {
                    parseAttribute(line)?.let { (key, value) -> attributes[key] = value }
                }
                else -> {
                    // Simple node without attributes
                    val nodeId = line.replace(";", "").trim()
                    if (nodeId.isNotEmpty() && !nodeId.contains("=")) {
                        nodes.add(GraphvizNode(nodeId.removeSurrounding("\"")))
                    }
                }
            }
            i++
        }
        
        return GraphvizGraph(
            name = graphInfo.name,
            type = graphInfo.type,
            nodes = nodes,
            edges = edges,
            attributes = attributes
        )
    }
    
    private fun parseGraphDeclaration(line: String): GraphInfo? {
        val regex = Regex("(digraph|graph|subgraph)\\s+(\\w+)\\s*\\{")
        val match = regex.find(line) ?: return null
        
        val typeStr = match.groupValues[1]
        val name = match.groupValues[2]
        
        val type = when (typeStr) {
            "digraph" -> GraphvizGraph.GraphType.DIGRAPH
            "graph" -> GraphvizGraph.GraphType.GRAPH
            "subgraph" -> GraphvizGraph.GraphType.SUBGRAPH
            else -> return null
        }
        
        return GraphInfo(name, type, type == GraphvizGraph.GraphType.DIGRAPH)
    }
    
    private fun parseNode(line: String): GraphvizNode? {
        val regex = Regex("(\\w+)\\s*\\[(.*)\\]")
        val match = regex.find(line) ?: return null
        
        val nodeId = match.groupValues[1].removeSurrounding("\"")
        val attributesStr = match.groupValues[2]
        
        val attributes = parseAttributeString(attributesStr)
        val label = attributes["label"] ?: nodeId
        
        return GraphvizNode(nodeId, label, attributes)
    }
    
    private fun parseEdge(line: String, isDirected: Boolean): GraphvizEdge? {
        val operator = if (isDirected) "->" else "--"
        val parts = line.split(operator)
        if (parts.size != 2) return null
        
        val from = parts[0].trim().removeSurrounding("\"")
        val toPart = parts[1].trim()
        
        val (to, attributes) = if (toPart.contains("[")) {
            val regex = Regex("(\\w+)\\s*\\[(.*)\\]")
            val match = regex.find(toPart)
            if (match != null) {
                val toNode = match.groupValues[1].removeSurrounding("\"")
                val attrs = parseAttributeString(match.groupValues[2])
                toNode to attrs
            } else {
                toPart.replace(";", "").trim().removeSurrounding("\"") to emptyMap()
            }
        } else {
            toPart.replace(";", "").trim().removeSurrounding("\"") to emptyMap()
        }
        
        val label = attributes["label"]
        
        return GraphvizEdge(from, to, label, attributes, isDirected)
    }
    
    private fun parseAttribute(line: String): Pair<String, String>? {
        val parts = line.replace(";", "").split("=", limit = 2)
        if (parts.size != 2) return null
        
        val key = parts[0].trim()
        val value = parts[1].trim().removeSurrounding("\"")
        
        return key to value
    }
    
    private fun parseAttributeString(attributesStr: String): Map<String, String> {
        val attributes = mutableMapOf<String, String>()
        
        // Simple parsing - split by comma and then by =
        val parts = attributesStr.split(",")
        for (part in parts) {
            val keyValue = part.split("=", limit = 2)
            if (keyValue.size == 2) {
                val key = keyValue[0].trim()
                val value = keyValue[1].trim().removeSurrounding("\"")
                attributes[key] = value
            }
        }
        
        return attributes
    }
    
    private data class GraphInfo(
        val name: String,
        val type: GraphvizGraph.GraphType,
        val isDirected: Boolean
    )
}
