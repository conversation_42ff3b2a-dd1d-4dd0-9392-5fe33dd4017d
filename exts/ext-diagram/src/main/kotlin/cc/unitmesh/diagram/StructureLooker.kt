package cc.unitmesh.diagram

import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import cc.unitmesh.diagram.graphviz.parser.GraphvizDotParser
import com.intellij.openapi.project.Project
import com.intellij.psi.PsiFile

/**
 * Structure looker for analyzing code structure and generating diagrams
 * Similar to JHipster UML functionality but for Graphviz
 */
class StructureLooker {

    /**
     * Analyze project structure and generate Graphviz DOT representation
     */
    fun analyzeProjectStructure(project: Project): String {
        // This would analyze the project structure and generate DOT format
        // For now, return a simple example
        return """
            digraph ProjectStructure {
                rankdir=TB;
                node [shape=box];

                "Project" -> "Modules";
                "Modules" -> "Core";
                "Modules" -> "Extensions";
                "Extensions" -> "ext-diagram";
                "Extensions" -> "ext-mermaid";
                "Extensions" -> "ext-plantuml";
            }
        """.trimIndent()
    }

    /**
     * Analyze a specific file and generate its structure diagram
     */
    fun analyzeFileStructure(psiFile: PsiFile): String {
        // This would analyze the file structure and generate DOT format
        // For now, return a simple example based on file type
        return when (psiFile.fileType.name.lowercase()) {
            "kotlin" -> generateKotlinStructure(psiFile)
            "java" -> generateJavaStructure(psiFile)
            else -> generateGenericStructure(psiFile)
        }
    }

    private fun generateKotlinStructure(psiFile: PsiFile): String {
        return """
            digraph KotlinFile {
                rankdir=TB;
                node [shape=box];

                "${psiFile.name}" [shape=ellipse];
                "${psiFile.name}" -> "Classes";
                "${psiFile.name}" -> "Functions";
                "${psiFile.name}" -> "Properties";
            }
        """.trimIndent()
    }

    private fun generateJavaStructure(psiFile: PsiFile): String {
        return """
            digraph JavaFile {
                rankdir=TB;
                node [shape=box];

                "${psiFile.name}" [shape=ellipse];
                "${psiFile.name}" -> "Classes";
                "${psiFile.name}" -> "Methods";
                "${psiFile.name}" -> "Fields";
            }
        """.trimIndent()
    }

    private fun generateGenericStructure(psiFile: PsiFile): String {
        return """
            digraph GenericFile {
                rankdir=TB;
                node [shape=box];

                "${psiFile.name}" [shape=ellipse];
                "${psiFile.name}" -> "Content";
            }
        """.trimIndent()
    }

    /**
     * Validate if the generated DOT content is valid
     */
    fun validateDotContent(dotContent: String): Boolean {
        return GraphvizDotParser.isValidDotContent(dotContent)
    }

    /**
     * Parse DOT content and return graph model
     */
    fun parseDotContent(dotContent: String): GraphvizGraph? {
        return GraphvizDotParser.parse(dotContent)
    }
}