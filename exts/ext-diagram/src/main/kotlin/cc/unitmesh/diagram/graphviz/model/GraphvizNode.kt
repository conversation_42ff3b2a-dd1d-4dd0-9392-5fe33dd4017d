package cc.unitmesh.diagram.graphviz.model

/**
 * Represents a node in a Graphviz graph
 * 
 * @property id Unique identifier for the node
 * @property label Display label for the node (optional, defaults to id)
 * @property attributes Node attributes (shape, color, style, etc.)
 */
data class GraphvizNode(
    val id: String,
    val label: String? = null,
    val attributes: Map<String, String> = emptyMap()
) {
    /**
     * Get the display label, falling back to id if label is null
     */
    fun getDisplayLabel(): String {
        return label ?: id
    }
    
    /**
     * Get a specific attribute value
     */
    fun getAttribute(key: String): String? {
        return attributes[key]
    }
    
    /**
     * Get the shape of the node (default is ellipse)
     */
    fun getShape(): String {
        return getAttribute("shape") ?: "ellipse"
    }
    
    /**
     * Get the color of the node
     */
    fun getColor(): String? {
        return getAttribute("color")
    }
    
    /**
     * Get the style of the node
     */
    fun getStyle(): String? {
        return getAttribute("style")
    }
    
    /**
     * Check if this node has a specific attribute
     */
    fun hasAttribute(key: String): Boolean {
        return attributes.containsKey(key)
    }
    
    /**
     * Generate DOT format string representation
     */
    fun toDotString(): String {
        val builder = StringBuilder()
        builder.append("\"$id\"")
        
        if (attributes.isNotEmpty() || label != null) {
            builder.append(" [")
            
            val attrs = mutableListOf<String>()
            
            // Add label if different from id
            if (label != null && label != id) {
                attrs.add("label=\"$label\"")
            }
            
            // Add other attributes
            attributes.forEach { (key, value) ->
                attrs.add("$key=\"$value\"")
            }
            
            builder.append(attrs.joinToString(", "))
            builder.append("]")
        }
        
        return builder.toString()
    }
    
    /**
     * Create a copy of this node with additional attributes
     */
    fun withAttributes(newAttributes: Map<String, String>): GraphvizNode {
        return copy(attributes = attributes + newAttributes)
    }
    
    /**
     * Create a copy of this node with a new label
     */
    fun withLabel(newLabel: String): GraphvizNode {
        return copy(label = newLabel)
    }
}
