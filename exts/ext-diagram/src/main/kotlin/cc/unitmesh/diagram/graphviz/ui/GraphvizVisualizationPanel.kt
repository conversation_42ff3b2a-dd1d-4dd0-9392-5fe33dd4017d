package cc.unitmesh.diagram.graphviz.ui

import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import cc.unitmesh.diagram.graphviz.model.GraphvizNode
import cc.unitmesh.diagram.graphviz.model.GraphvizEdge
import com.intellij.openapi.Disposable
import com.intellij.openapi.project.Project
import com.intellij.ui.components.JBScrollPane
import com.intellij.util.ui.JBUI
import guru.nidi.graphviz.engine.Format
import guru.nidi.graphviz.engine.Graphviz
import guru.nidi.graphviz.model.Factory
import guru.nidi.graphviz.model.MutableGraph
import guru.nidi.graphviz.model.MutableNode
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Graphics
import java.awt.Graphics2D
import java.awt.image.BufferedImage
import java.io.ByteArrayInputStream
import javax.imageio.ImageIO
import javax.swing.JLabel
import javax.swing.JPanel
import javax.swing.SwingConstants

/**
 * Panel for visualizing Graphviz graphs
 * Renders the graph as an image using the graphviz-java library
 */
class GraphvizVisualizationPanel(
    private val project: Project
) : JPanel(BorderLayout()), Disposable {
    
    private var currentGraph: GraphvizGraph? = null
    private var renderedImage: BufferedImage? = null
    private val imagePanel = ImageDisplayPanel()
    private val scrollPane = JBScrollPane(imagePanel)
    
    init {
        setupUI()
    }
    
    private fun setupUI() {
        add(scrollPane, BorderLayout.CENTER)
        
        // Set preferred size
        preferredSize = Dimension(400, 300)
        
        // Add some padding
        border = JBUI.Borders.empty(10)
        
        // Show initial message
        showMessage("No graph to display")
    }
    
    fun updateGraph(graph: GraphvizGraph) {
        this.currentGraph = graph
        renderGraph(graph)
    }
    
    private fun renderGraph(graph: GraphvizGraph) {
        try {
            // Convert our model back to graphviz-java model for rendering
            val mutableGraph = convertToMutableGraph(graph)
            
            // Render to SVG first, then convert to image
            val svgBytes = Graphviz.fromGraph(mutableGraph)
                .render(Format.SVG)
                .toByteArray()
            
            // For now, render as PNG for easier display
            val pngBytes = Graphviz.fromGraph(mutableGraph)
                .render(Format.PNG)
                .toByteArray()
            
            val image = ImageIO.read(ByteArrayInputStream(pngBytes))
            renderedImage = image
            
            imagePanel.setImage(image)
            imagePanel.revalidate()
            imagePanel.repaint()
            
        } catch (e: Exception) {
            showMessage("Error rendering graph: ${e.message}")
        }
    }
    
    private fun convertToMutableGraph(graph: GraphvizGraph): MutableGraph {
        val mutableGraph = when (graph.type) {
            GraphvizGraph.GraphType.DIGRAPH -> Factory.mutGraph(graph.name).setDirected(true)
            GraphvizGraph.GraphType.GRAPH -> Factory.mutGraph(graph.name).setDirected(false)
            GraphvizGraph.GraphType.SUBGRAPH -> Factory.mutGraph(graph.name).setDirected(true)
        }
        
        // Add nodes
        val nodeMap = mutableMapOf<String, MutableNode>()
        graph.nodes.forEach { node ->
            val mutableNode = Factory.mutNode(node.id)
            if (node.label != null && node.label != node.id) {
                mutableNode.add("label", node.label)
            }
            nodeMap[node.id] = mutableNode
            mutableGraph.add(mutableNode)
        }
        
        // Add edges
        graph.edges.forEach { edge ->
            val fromNode = nodeMap[edge.from]
            val toNode = nodeMap[edge.to]
            
            if (fromNode != null && toNode != null) {
                val link = fromNode.linkTo(toNode)
                if (edge.label != null) {
                    link.add("label", edge.label)
                }
            }
        }
        
        return mutableGraph
    }
    
    private fun showMessage(message: String) {
        imagePanel.setMessage(message)
        imagePanel.revalidate()
        imagePanel.repaint()
    }
    
    override fun dispose() {
        renderedImage?.flush()
        renderedImage = null
    }
    
    /**
     * Inner panel for displaying the rendered graph image
     */
    private class ImageDisplayPanel : JPanel() {
        private var image: BufferedImage? = null
        private var message: String? = null
        
        fun setImage(image: BufferedImage) {
            this.image = image
            this.message = null
            preferredSize = Dimension(image.width, image.height)
        }
        
        fun setMessage(message: String) {
            this.message = message
            this.image = null
            preferredSize = Dimension(200, 100)
        }
        
        override fun paintComponent(g: Graphics) {
            super.paintComponent(g)
            
            val g2d = g as Graphics2D
            
            image?.let { img ->
                // Center the image in the panel
                val x = (width - img.width) / 2
                val y = (height - img.height) / 2
                g2d.drawImage(img, x, y, null)
            }
            
            message?.let { msg ->
                // Draw centered message
                val fm = g2d.fontMetrics
                val x = (width - fm.stringWidth(msg)) / 2
                val y = (height + fm.ascent) / 2
                g2d.drawString(msg, x, y)
            }
        }
    }
}
