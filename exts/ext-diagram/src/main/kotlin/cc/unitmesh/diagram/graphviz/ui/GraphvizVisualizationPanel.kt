package cc.unitmesh.diagram.graphviz.ui

import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import cc.unitmesh.diagram.graphviz.model.GraphvizNode
import cc.unitmesh.diagram.graphviz.model.GraphvizEdge
import com.intellij.openapi.Disposable
import com.intellij.openapi.project.Project
import com.intellij.ui.components.JBScrollPane
import com.intellij.util.ui.JBUI
import java.awt.BorderLayout
import java.awt.Dimension
import java.awt.Graphics
import java.awt.Graphics2D
import java.awt.Color
import java.awt.Font
import java.awt.FontMetrics
import java.awt.BasicStroke
import java.awt.geom.Ellipse2D
import java.awt.geom.Rectangle2D
import java.awt.geom.Line2D
import javax.swing.JLabel
import javax.swing.JPanel
import javax.swing.SwingConstants
import kotlin.math.*

/**
 * Panel for visualizing Graphviz graphs
 * Simple implementation using Java2D for rendering
 */
class GraphvizVisualizationPanel(
    private val project: Project
) : JPanel(BorderLayout()), Disposable {
    
    private var currentGraph: GraphvizGraph? = null
    private val graphPanel = GraphRenderPanel()
    private val scrollPane = JBScrollPane(graphPanel)
    
    init {
        setupUI()
    }
    
    private fun setupUI() {
        add(scrollPane, BorderLayout.CENTER)
        
        // Set preferred size
        preferredSize = Dimension(400, 300)
        
        // Add some padding
        border = JBUI.Borders.empty(10)
        
        // Show initial message
        showMessage("No graph to display")
    }
    
    fun updateGraph(graph: GraphvizGraph) {
        this.currentGraph = graph
        graphPanel.setGraph(graph)
        graphPanel.revalidate()
        graphPanel.repaint()
    }
    
    private fun showMessage(message: String) {
        graphPanel.setMessage(message)
        graphPanel.revalidate()
        graphPanel.repaint()
    }
    
    override fun dispose() {
        currentGraph = null
    }
    
    /**
     * Inner panel for rendering the graph using Java2D
     */
    private class GraphRenderPanel : JPanel() {
        private var graph: GraphvizGraph? = null
        private var message: String? = null
        private var nodePositions = mutableMapOf<String, Point>()
        
        fun setGraph(graph: GraphvizGraph) {
            this.graph = graph
            this.message = null
            calculateLayout(graph)
            preferredSize = Dimension(600, 400)
        }
        
        fun setMessage(message: String) {
            this.message = message
            this.graph = null
            preferredSize = Dimension(200, 100)
        }
        
        private fun calculateLayout(graph: GraphvizGraph) {
            nodePositions.clear()
            val nodes = graph.getAllNodes()
            
            if (nodes.isEmpty()) return
            
            // Simple circular layout
            val centerX = 300.0
            val centerY = 200.0
            val radius = min(150.0, (nodes.size * 30).toDouble())
            
            nodes.forEachIndexed { index, node ->
                val angle = 2 * PI * index / nodes.size
                val x = centerX + radius * cos(angle)
                val y = centerY + radius * sin(angle)
                nodePositions[node.id] = Point(x.toInt(), y.toInt())
            }
        }
        
        override fun paintComponent(g: Graphics) {
            super.paintComponent(g)
            
            val g2d = g as Graphics2D
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON)
            
            graph?.let { graph ->
                drawGraph(g2d, graph)
            }
            
            message?.let { msg ->
                drawMessage(g2d, msg)
            }
        }
        
        private fun drawGraph(g2d: Graphics2D, graph: GraphvizGraph) {
            // Draw edges first (so they appear behind nodes)
            g2d.color = Color.DARK_GRAY
            g2d.stroke = BasicStroke(2f)
            
            graph.getAllEdges().forEach { edge ->
                drawEdge(g2d, edge)
            }
            
            // Draw nodes
            graph.getAllNodes().forEach { node ->
                drawNode(g2d, node)
            }
        }
        
        private fun drawNode(g2d: Graphics2D, node: GraphvizNode) {
            val pos = nodePositions[node.id] ?: return
            
            val nodeSize = 60
            val x = pos.x - nodeSize / 2
            val y = pos.y - nodeSize / 2
            
            // Draw node shape based on attributes
            val shape = node.getShape()
            when (shape) {
                "box", "rectangle" -> {
                    g2d.color = Color.LIGHT_GRAY
                    g2d.fillRect(x, y, nodeSize, nodeSize)
                    g2d.color = Color.BLACK
                    g2d.drawRect(x, y, nodeSize, nodeSize)
                }
                "diamond" -> {
                    val diamond = intArrayOf(pos.x, pos.y - nodeSize/2, pos.x + nodeSize/2, pos.y, pos.x, pos.y + nodeSize/2, pos.x - nodeSize/2, pos.y)
                    val xPoints = intArrayOf(diamond[0], diamond[2], diamond[4], diamond[6])
                    val yPoints = intArrayOf(diamond[1], diamond[3], diamond[5], diamond[7])
                    g2d.color = Color.LIGHT_GRAY
                    g2d.fillPolygon(xPoints, yPoints, 4)
                    g2d.color = Color.BLACK
                    g2d.drawPolygon(xPoints, yPoints, 4)
                }
                else -> { // ellipse (default)
                    g2d.color = Color.LIGHT_GRAY
                    g2d.fillOval(x, y, nodeSize, nodeSize)
                    g2d.color = Color.BLACK
                    g2d.drawOval(x, y, nodeSize, nodeSize)
                }
            }
            
            // Draw label
            g2d.color = Color.BLACK
            g2d.font = Font("SansSerif", Font.PLAIN, 12)
            val fm = g2d.fontMetrics
            val label = node.getDisplayLabel()
            val labelWidth = fm.stringWidth(label)
            val labelX = pos.x - labelWidth / 2
            val labelY = pos.y + fm.ascent / 2
            g2d.drawString(label, labelX, labelY)
        }
        
        private fun drawEdge(g2d: Graphics2D, edge: GraphvizEdge) {
            val fromPos = nodePositions[edge.from] ?: return
            val toPos = nodePositions[edge.to] ?: return
            
            // Draw line
            g2d.drawLine(fromPos.x, fromPos.y, toPos.x, toPos.y)
            
            // Draw arrow for directed edges
            if (edge.isDirected) {
                drawArrow(g2d, fromPos, toPos)
            }
            
            // Draw edge label if present
            edge.label?.let { label ->
                val midX = (fromPos.x + toPos.x) / 2
                val midY = (fromPos.y + toPos.y) / 2
                g2d.font = Font("SansSerif", Font.PLAIN, 10)
                g2d.drawString(label, midX, midY)
            }
        }
        
        private fun drawArrow(g2d: Graphics2D, from: Point, to: Point) {
            val arrowLength = 10
            val arrowAngle = PI / 6
            
            val dx = to.x - from.x
            val dy = to.y - from.y
            val angle = atan2(dy.toDouble(), dx.toDouble())
            
            // Calculate arrow tip position (on the edge of the target node)
            val nodeRadius = 30
            val tipX = to.x - nodeRadius * cos(angle)
            val tipY = to.y - nodeRadius * sin(angle)
            
            // Calculate arrow wing positions
            val wing1X = tipX - arrowLength * cos(angle - arrowAngle)
            val wing1Y = tipY - arrowLength * sin(angle - arrowAngle)
            val wing2X = tipX - arrowLength * cos(angle + arrowAngle)
            val wing2Y = tipY - arrowLength * sin(angle + arrowAngle)
            
            // Draw arrow
            g2d.drawLine(tipX.toInt(), tipY.toInt(), wing1X.toInt(), wing1Y.toInt())
            g2d.drawLine(tipX.toInt(), tipY.toInt(), wing2X.toInt(), wing2Y.toInt())
        }
        
        private fun drawMessage(g2d: Graphics2D, message: String) {
            g2d.color = Color.GRAY
            g2d.font = Font("SansSerif", Font.PLAIN, 14)
            val fm = g2d.fontMetrics
            val x = (width - fm.stringWidth(message)) / 2
            val y = (height + fm.ascent) / 2
            g2d.drawString(message, x, y)
        }
    }
    
    private data class Point(val x: Int, val y: Int)
}
