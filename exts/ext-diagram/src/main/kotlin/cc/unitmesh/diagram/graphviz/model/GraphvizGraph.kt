package cc.unitmesh.diagram.graphviz.model

/**
 * Represents a Graphviz graph structure
 * 
 * @property name The name of the graph
 * @property type The type of graph (digraph, graph, subgraph)
 * @property nodes List of nodes in the graph
 * @property edges List of edges in the graph
 * @property attributes Graph-level attributes
 * @property subgraphs List of subgraphs
 */
data class GraphvizGraph(
    val name: String,
    val type: GraphType,
    val nodes: List<GraphvizNode> = emptyList(),
    val edges: List<GraphvizEdge> = emptyList(),
    val attributes: Map<String, String> = emptyMap(),
    val subgraphs: List<GraphvizGraph> = emptyList()
) {
    enum class GraphType {
        GRAPH,      // undirected graph
        DIGRAPH,    // directed graph
        SUBGRAPH    // subgraph
    }
    
    /**
     * Get all nodes including those in subgraphs
     */
    fun getAllNodes(): List<GraphvizNode> {
        val allNodes = mutableListOf<GraphvizNode>()
        allNodes.addAll(nodes)
        subgraphs.forEach { subgraph ->
            allNodes.addAll(subgraph.getAllNodes())
        }
        return allNodes
    }
    
    /**
     * Get all edges including those in subgraphs
     */
    fun getAllEdges(): List<GraphvizEdge> {
        val allEdges = mutableListOf<GraphvizEdge>()
        allEdges.addAll(edges)
        subgraphs.forEach { subgraph ->
            allEdges.addAll(subgraph.getAllEdges())
        }
        return allEdges
    }
    
    /**
     * Find a node by its ID
     */
    fun findNode(nodeId: String): GraphvizNode? {
        return getAllNodes().find { it.id == nodeId }
    }
    
    /**
     * Check if this is a directed graph
     */
    fun isDirected(): Boolean {
        return type == GraphType.DIGRAPH
    }
    
    /**
     * Generate DOT format string representation
     */
    fun toDotString(): String {
        val builder = StringBuilder()
        
        val graphKeyword = when (type) {
            GraphType.GRAPH -> "graph"
            GraphType.DIGRAPH -> "digraph"
            GraphType.SUBGRAPH -> "subgraph"
        }
        
        builder.append("$graphKeyword $name {\n")
        
        // Add graph attributes
        attributes.forEach { (key, value) ->
            builder.append("  $key=\"$value\";\n")
        }
        
        // Add nodes
        nodes.forEach { node ->
            builder.append("  ${node.toDotString()};\n")
        }
        
        // Add edges
        val edgeOperator = if (isDirected()) "->" else "--"
        edges.forEach { edge ->
            builder.append("  ${edge.toDotString(edgeOperator)};\n")
        }
        
        // Add subgraphs
        subgraphs.forEach { subgraph ->
            val subgraphDot = subgraph.toDotString()
            builder.append("  ${subgraphDot.prependIndent("  ")}\n")
        }
        
        builder.append("}")
        return builder.toString()
    }
}
