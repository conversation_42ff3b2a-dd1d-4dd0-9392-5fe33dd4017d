package cc.unitmesh.diagram.graphviz

import cc.unitmesh.devti.sketch.ui.ExtensionLangSketch
import cc.unitmesh.devti.sketch.ui.LanguageSketchProvider
import cc.unitmesh.diagram.graphviz.parser.GraphvizDotParser
import com.intellij.openapi.project.Project
import com.intellij.testFramework.LightVirtualFile

/**
 * Provider for Graphviz DOT language sketches
 * Supports rendering of Graphviz DOT format diagrams
 */
class GraphvizSketchProvider : LanguageSketchProvider {
    
    override fun isSupported(lang: String): Boolean {
        return lang.lowercase() in supportedLanguages
    }
    
    override fun create(project: Project, content: String): ExtensionLangSketch {
        // Validate if content is valid DOT format
        val isValidDot = GraphvizDotParser.isValidDotContent(content)
        
        if (isValidDot) {
            val virtualFile = LightVirtualFile("graphviz.dot", content)
            return GraphvizSketch(project, virtualFile, content)
        } else {
            // Fallback to simple text highlighting if not valid DOT
            val virtualFile = LightVirtualFile("graphviz.dot", content)
            return GraphvizSketch(project, virtualFile, content, isValidDot = false)
        }
    }
    
    companion object {
        private val supportedLanguages = setOf(
            "dot",
            "graphviz",
            "gv"
        )
    }
}
