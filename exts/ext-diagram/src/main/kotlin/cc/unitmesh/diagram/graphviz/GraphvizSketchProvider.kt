package cc.unitmesh.diagram.graphviz

import cc.unitmesh.devti.sketch.ui.ExtensionLangSketch
import cc.unitmesh.devti.sketch.ui.LanguageSketchProvider
import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import cc.unitmesh.diagram.graphviz.parser.GraphvizDotParser
import cc.unitmesh.diagram.graphviz.ui.GraphvizVisualizationPanel
import com.intellij.openapi.project.Project
import com.intellij.util.ui.JBUI
import javax.swing.JComponent
import javax.swing.JPanel
import javax.swing.JLabel
import javax.swing.SwingConstants
import java.awt.BorderLayout

/**
 * LanguageSketchProvider implementation for Graphviz DOT format
 */
class GraphvizSketchProvider : LanguageSketchProvider {
    override fun isSupported(lang: String): Boolean {
        return lang == "graphviz" || lang == "dot" || lang == "gv"
    }

    override fun create(project: Project, content: String): ExtensionLangSketch {
        return GraphvizSketch(project, content)
    }
}

/**
 * ExtensionLangSketch implementation for Graphviz
 */
class GraphvizSketch(
    private val project: Project,
    private var content: String
) : ExtensionLangSketch {
    
    private val mainPanel: JPanel = JPanel(BorderLayout())
    private val visualizationPanel: GraphvizVisualizationPanel = GraphvizVisualizationPanel(project)
    private var currentGraph: GraphvizGraph? = null
    
    init {
        setupUI()
        parseAndUpdateGraph(content)
    }
    
    private fun setupUI() {
        mainPanel.border = JBUI.Borders.empty(10)
        mainPanel.add(visualizationPanel, BorderLayout.CENTER)
    }
    
    private fun parseAndUpdateGraph(dotContent: String) {
        try {
            val graph = GraphvizDotParser.parse(dotContent)
            if (graph != null) {
                currentGraph = graph
                visualizationPanel.updateGraph(graph)
            } else {
                showError("Failed to parse Graphviz DOT content")
            }
        } catch (e: Exception) {
            showError("Error parsing Graphviz content: ${e.message}")
        }
    }
    
    private fun showError(message: String) {
        mainPanel.removeAll()
        val errorLabel = JLabel(message, SwingConstants.CENTER)
        mainPanel.add(errorLabel, BorderLayout.CENTER)
        mainPanel.revalidate()
        mainPanel.repaint()
    }
    
    override fun getExtensionName(): String = "graphviz"
    
    override fun getViewText(): String = content
    
    override fun updateViewText(text: String, complete: Boolean) {
        this.content = text
        if (complete) {
            parseAndUpdateGraph(text)
        }
    }
    
    override fun onComplete(code: String) {
        updateViewText(code, true)
    }
    
    override fun onDoneStream(allText: String) {
        updateViewText(allText, true)
    }
    
    override fun getComponent(): JComponent = mainPanel
    
    override fun dispose() {
        visualizationPanel.dispose()
    }
    
    /**
     * Get the current parsed graph
     */
    fun getCurrentGraph(): GraphvizGraph? = currentGraph
}
