package cc.unitmesh.diagram.graphviz.model

/**
 * Represents an edge in a Graphviz graph
 * 
 * @property from Source node ID
 * @property to Target node ID
 * @property label Display label for the edge (optional)
 * @property attributes Edge attributes (color, style, weight, etc.)
 * @property isDirected Whether this edge is directed
 */
data class GraphvizEdge(
    val from: String,
    val to: String,
    val label: String? = null,
    val attributes: Map<String, String> = emptyMap(),
    val isDirected: Boolean = true
) {
    /**
     * Get a specific attribute value
     */
    fun getAttribute(key: String): String? {
        return attributes[key]
    }
    
    /**
     * Get the color of the edge
     */
    fun getColor(): String? {
        return getAttribute("color")
    }
    
    /**
     * Get the style of the edge
     */
    fun getStyle(): String? {
        return getAttribute("style")
    }
    
    /**
     * Get the weight of the edge
     */
    fun getWeight(): String? {
        return getAttribute("weight")
    }
    
    /**
     * Get the arrowhead style
     */
    fun getArrowhead(): String? {
        return getAttribute("arrowhead")
    }
    
    /**
     * Get the arrowtail style
     */
    fun getArrowtail(): String? {
        return getAttribute("arrowtail")
    }
    
    /**
     * Check if this edge has a specific attribute
     */
    fun hasAttribute(key: String): Boolean {
        return attributes.containsKey(key)
    }
    
    /**
     * Generate DOT format string representation
     */
    fun toDotString(edgeOperator: String = if (isDirected) "->" else "--"): String {
        val builder = StringBuilder()
        builder.append("\"$from\" $edgeOperator \"$to\"")
        
        if (attributes.isNotEmpty() || label != null) {
            builder.append(" [")
            
            val attrs = mutableListOf<String>()
            
            // Add label if present
            if (label != null) {
                attrs.add("label=\"$label\"")
            }
            
            // Add other attributes
            attributes.forEach { (key, value) ->
                attrs.add("$key=\"$value\"")
            }
            
            builder.append(attrs.joinToString(", "))
            builder.append("]")
        }
        
        return builder.toString()
    }
    
    /**
     * Create a copy of this edge with additional attributes
     */
    fun withAttributes(newAttributes: Map<String, String>): GraphvizEdge {
        return copy(attributes = attributes + newAttributes)
    }
    
    /**
     * Create a copy of this edge with a new label
     */
    fun withLabel(newLabel: String): GraphvizEdge {
        return copy(label = newLabel)
    }
    
    /**
     * Create a copy of this edge with reversed direction
     */
    fun reverse(): GraphvizEdge {
        return copy(from = to, to = from)
    }
    
    /**
     * Check if this edge connects the specified nodes (regardless of direction)
     */
    fun connects(nodeId1: String, nodeId2: String): Boolean {
        return (from == nodeId1 && to == nodeId2) || 
               (!isDirected && from == nodeId2 && to == nodeId1)
    }
    
    /**
     * Get the other end of the edge given one node
     */
    fun getOtherEnd(nodeId: String): String? {
        return when (nodeId) {
            from -> to
            to -> if (!isDirected) from else null
            else -> null
        }
    }
}
