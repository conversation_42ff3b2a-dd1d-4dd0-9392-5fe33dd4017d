package cc.unitmesh.diagram.graphviz.model

/**
 * Represents a Graphviz graph structure
 */
data class GraphvizGraph(
    val name: String,
    val type: GraphType,
    val nodes: List<GraphvizNode>,
    val edges: List<GraphvizEdge>,
    val attributes: Map<String, String> = emptyMap()
) {
    enum class GraphType {
        GRAPH,      // undirected graph
        DIGRAPH,    // directed graph
        SUBGRAPH    // subgraph
    }
}

/**
 * Represents a node in a Graphviz graph
 */
data class GraphvizNode(
    val id: String,
    val label: String? = null,
    val attributes: Map<String, String> = emptyMap()
) {
    fun getDisplayLabel(): String = label ?: id
}

/**
 * Represents an edge in a Graphviz graph
 */
data class GraphvizEdge(
    val from: String,
    val to: String,
    val label: String? = null,
    val attributes: Map<String, String> = emptyMap(),
    val isDirected: Boolean = true
)

/**
 * Represents a cluster/subgraph in Graphviz
 */
data class GraphvizCluster(
    val name: String,
    val nodes: List<GraphvizNode>,
    val edges: List<GraphvizEdge>,
    val attributes: Map<String, String> = emptyMap()
)
