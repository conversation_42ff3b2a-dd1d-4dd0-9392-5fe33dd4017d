package cc.unitmesh.diagram.graphviz

import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import cc.unitmesh.diagram.graphviz.model.GraphvizNode
import cc.unitmesh.diagram.graphviz.model.GraphvizEdge
import cc.unitmesh.diagram.graphviz.parser.GraphvizDotParser

/**
 * Builder for creating Graphviz diagrams programmatically
 */
class GraphvizDiagramBuilder {
    private var name: String = "G"
    private var type: GraphvizGraph.GraphType = GraphvizGraph.GraphType.DIGRAPH
    private val nodes = mutableListOf<GraphvizNode>()
    private val edges = mutableListOf<GraphvizEdge>()
    private val attributes = mutableMapOf<String, String>()
    private val subgraphs = mutableListOf<GraphvizGraph>()
    
    /**
     * Set the graph name
     */
    fun name(name: String): GraphvizDiagramBuilder {
        this.name = name
        return this
    }
    
    /**
     * Set the graph type
     */
    fun type(type: GraphvizGraph.GraphType): GraphvizDiagramBuilder {
        this.type = type
        return this
    }
    
    /**
     * Make this a directed graph
     */
    fun directed(): GraphvizDiagramBuilder {
        this.type = GraphvizGraph.GraphType.DIGRAPH
        return this
    }
    
    /**
     * Make this an undirected graph
     */
    fun undirected(): GraphvizDiagramBuilder {
        this.type = GraphvizGraph.GraphType.GRAPH
        return this
    }
    
    /**
     * Add a node to the graph
     */
    fun addNode(node: GraphvizNode): GraphvizDiagramBuilder {
        nodes.add(node)
        return this
    }
    
    /**
     * Add a node with just an ID
     */
    fun addNode(id: String): GraphvizDiagramBuilder {
        return addNode(GraphvizNode(id))
    }
    
    /**
     * Add a node with ID and label
     */
    fun addNode(id: String, label: String): GraphvizDiagramBuilder {
        return addNode(GraphvizNode(id, label))
    }
    
    /**
     * Add a node with ID, label, and attributes
     */
    fun addNode(id: String, label: String, attributes: Map<String, String>): GraphvizDiagramBuilder {
        return addNode(GraphvizNode(id, label, attributes))
    }
    
    /**
     * Add an edge to the graph
     */
    fun addEdge(edge: GraphvizEdge): GraphvizDiagramBuilder {
        edges.add(edge)
        return this
    }
    
    /**
     * Add an edge between two nodes
     */
    fun addEdge(from: String, to: String): GraphvizDiagramBuilder {
        return addEdge(GraphvizEdge(from, to, isDirected = type == GraphvizGraph.GraphType.DIGRAPH))
    }
    
    /**
     * Add an edge with a label
     */
    fun addEdge(from: String, to: String, label: String): GraphvizDiagramBuilder {
        return addEdge(GraphvizEdge(from, to, label, isDirected = type == GraphvizGraph.GraphType.DIGRAPH))
    }
    
    /**
     * Add an edge with label and attributes
     */
    fun addEdge(from: String, to: String, label: String, attributes: Map<String, String>): GraphvizDiagramBuilder {
        return addEdge(GraphvizEdge(from, to, label, attributes, type == GraphvizGraph.GraphType.DIGRAPH))
    }
    
    /**
     * Add a graph-level attribute
     */
    fun attribute(key: String, value: String): GraphvizDiagramBuilder {
        attributes[key] = value
        return this
    }
    
    /**
     * Add multiple graph-level attributes
     */
    fun attributes(attrs: Map<String, String>): GraphvizDiagramBuilder {
        attributes.putAll(attrs)
        return this
    }
    
    /**
     * Add a subgraph
     */
    fun addSubgraph(subgraph: GraphvizGraph): GraphvizDiagramBuilder {
        subgraphs.add(subgraph)
        return this
    }
    
    /**
     * Build the graph
     */
    fun build(): GraphvizGraph {
        return GraphvizGraph(
            name = name,
            type = type,
            nodes = nodes.toList(),
            edges = edges.toList(),
            attributes = attributes.toMap(),
            subgraphs = subgraphs.toList()
        )
    }
    
    companion object {
        /**
         * Create a new builder
         */
        fun create(): GraphvizDiagramBuilder {
            return GraphvizDiagramBuilder()
        }
        
        /**
         * Create a builder from DOT content
         */
        fun fromDot(dotContent: String): GraphvizDiagramBuilder? {
            val graph = GraphvizDotParser.parse(dotContent) ?: return null
            
            return GraphvizDiagramBuilder().apply {
                name(graph.name)
                type(graph.type)
                attributes(graph.attributes)
                
                graph.nodes.forEach { addNode(it) }
                graph.edges.forEach { addEdge(it) }
                graph.subgraphs.forEach { addSubgraph(it) }
            }
        }
        
        /**
         * Create a simple directed graph
         */
        fun digraph(name: String = "G"): GraphvizDiagramBuilder {
            return create().name(name).directed()
        }
        
        /**
         * Create a simple undirected graph
         */
        fun graph(name: String = "G"): GraphvizDiagramBuilder {
            return create().name(name).undirected()
        }
    }
}
