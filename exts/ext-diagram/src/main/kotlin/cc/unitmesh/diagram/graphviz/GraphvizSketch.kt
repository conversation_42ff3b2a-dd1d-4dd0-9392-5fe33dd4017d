package cc.unitmesh.diagram.graphviz

import cc.unitmesh.devti.sketch.ui.ExtensionLangSketch
import cc.unitmesh.devti.sketch.ui.code.CodeHighlightSketch
import cc.unitmesh.diagram.graphviz.parser.GraphvizDotParser
import cc.unitmesh.diagram.graphviz.ui.GraphvizVisualizationPanel
import com.intellij.lang.Language
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.fileEditor.impl.text.TextEditorProvider
import com.intellij.openapi.project.Project
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.ui.dsl.builder.panel
import com.intellij.util.ui.JBUI
import javax.swing.JComponent
import javax.swing.JPanel
import javax.swing.JSplitPane

/**
 * Graphviz sketch implementation that provides both text editing and visual preview
 * Similar to PlantUML and Mermaid sketches
 */
class GraphvizSketch(
    private val project: Project,
    private val virtualFile: VirtualFile,
    private var content: String,
    private val isValidDot: Boolean = true
) : ExtensionLangSketch {
    
    private var mainPanel: JPanel
    private var textEditor: TextEditor? = null
    private var visualizationPanel: GraphvizVisualizationPanel? = null
    
    init {
        mainPanel = createMainPanel()
    }
    
    private fun createMainPanel(): JPanel {
        if (!isValidDot) {
            // If not valid DOT, just show text editor with syntax highlighting
            return createTextOnlyPanel()
        }
        
        // Create split panel with text editor and visualization
        val textEditor = TextEditorProvider.getInstance().createEditor(project, virtualFile) as TextEditor
        this.textEditor = textEditor
        
        val visualizationPanel = GraphvizVisualizationPanel(project)
        this.visualizationPanel = visualizationPanel
        
        // Update visualization with initial content
        updateVisualization(content)
        
        val splitPane = JSplitPane(JSplitPane.HORIZONTAL_SPLIT).apply {
            leftComponent = textEditor.component
            rightComponent = visualizationPanel
            resizeWeight = 0.5
            dividerLocation = 400
        }
        
        return panel {
            row {
                cell(splitPane).align(com.intellij.ui.dsl.builder.Align.FILL)
            }
        }.apply {
            border = JBUI.Borders.empty(0, 10)
        }
    }
    
    private fun createTextOnlyPanel(): JPanel {
        val textEditor = TextEditorProvider.getInstance().createEditor(project, virtualFile) as TextEditor
        this.textEditor = textEditor
        
        return panel {
            row {
                cell(textEditor.component).align(com.intellij.ui.dsl.builder.Align.FILL)
            }
        }.apply {
            border = JBUI.Borders.empty(0, 10)
        }
    }
    
    private fun updateVisualization(dotContent: String) {
        visualizationPanel?.let { panel ->
            val graph = GraphvizDotParser.parse(dotContent)
            if (graph != null) {
                panel.updateGraph(graph)
            }
        }
    }
    
    override fun getExtensionName(): String = "Graphviz"
    
    override fun getViewText(): String = content
    
    override fun updateViewText(text: String, complete: Boolean) {
        this.content = text
        virtualFile.setBinaryContent(text.toByteArray())
        
        if (complete && isValidDot) {
            updateVisualization(text)
        }
    }
    
    override fun onDoneStream(allText: String) {
        if (isValidDot) {
            updateVisualization(allText)
        }
    }
    
    override fun onComplete(code: String) {
        if (isValidDot) {
            updateVisualization(code)
        }
    }
    
    override fun getComponent(): JComponent = mainPanel
    
    override fun dispose() {
        textEditor?.let { 
            // Dispose text editor if needed
        }
        visualizationPanel?.dispose()
    }
}
