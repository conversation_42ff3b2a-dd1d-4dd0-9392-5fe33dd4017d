package cc.unitmesh.diagram.graphviz

import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import cc.unitmesh.diagram.graphviz.parser.GraphvizDotParser
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class GraphvizTest {
    
    @Test
    fun testGraphvizDotParser() {
        val dotContent = """
            digraph G {
                A [label="Start"];
                B [label="End"];
                A -> B [label="flow"];
            }
        """.trimIndent()
        
        val graph = GraphvizDotParser.parse(dotContent)
        assertNotNull(graph)
        assertEquals("G", graph.name)
        assertEquals(GraphvizGraph.GraphType.DIGRAPH, graph.type)
        assertTrue(graph.isDirected())
    }
    
    @Test
    fun testGraphvizDiagramBuilder() {
        val graph = GraphvizDiagramBuilder
            .digraph("TestGraph")
            .addNode("A", "Start")
            .addNode("B", "End")
            .addEdge("A", "B", "flow")
            .attribute("rankdir", "LR")
            .build()
        
        assertEquals("TestGraph", graph.name)
        assertEquals(GraphvizGraph.GraphType.DIGRAPH, graph.type)
        assertEquals(2, graph.nodes.size)
        assertEquals(1, graph.edges.size)
        assertEquals("LR", graph.attributes["rankdir"])
    }
    
    @Test
    fun testGraphvizSketchProvider() {
        val provider = GraphvizSketchProvider()
        
        assertTrue(provider.isSupported("graphviz"))
        assertTrue(provider.isSupported("dot"))
        assertTrue(provider.isSupported("gv"))
    }
    
    @Test
    fun testDotStringGeneration() {
        val graph = GraphvizDiagramBuilder
            .digraph("Test")
            .addNode("A", "Node A")
            .addNode("B", "Node B")
            .addEdge("A", "B", "edge")
            .build()
        
        val dotString = graph.toDotString()
        assertTrue(dotString.contains("digraph Test"))
        assertTrue(dotString.contains("A"))
        assertTrue(dotString.contains("B"))
        assertTrue(dotString.contains("A -> B"))
    }
}
