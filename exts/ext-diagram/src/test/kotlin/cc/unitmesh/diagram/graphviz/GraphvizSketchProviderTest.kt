package cc.unitmesh.diagram.graphviz

import com.intellij.testFramework.fixtures.BasePlatformTestCase
import org.junit.Test

class GraphvizSketchProviderTest : BasePlatformTestCase() {
    
    private lateinit var provider: GraphvizSketchProvider
    
    override fun setUp() {
        super.setUp()
        provider = GraphvizSketchProvider()
    }
    
    @Test
    fun `should support dot language`() {
        assertTrue("Should support 'dot'", provider.isSupported("dot"))
        assertTrue("Should support 'DOT'", provider.isSupported("DOT"))
    }
    
    @Test
    fun `should support graphviz language`() {
        assertTrue("Should support 'graphviz'", provider.isSupported("graphviz"))
        assertTrue("Should support 'GRAPHVIZ'", provider.isSupported("GRAPHVIZ"))
    }
    
    @Test
    fun `should support gv language`() {
        assertTrue("Should support 'gv'", provider.isSupported("gv"))
        assertTrue("Should support 'GV'", provider.isSupported("GV"))
    }
    
    @Test
    fun `should not support unsupported languages`() {
        assertFalse("Should not support 'java'", provider.isSupported("java"))
        assertFalse("Should not support 'python'", provider.isSupported("python"))
        assertFalse("Should not support 'plantuml'", provider.isSupported("plantuml"))
    }
    
    @Test
    fun `should create sketch for valid DOT content`() {
        val validDotContent = """
            digraph G {
                A -> B;
                B -> C;
            }
        """.trimIndent()
        
        val sketch = provider.create(project, validDotContent)
        
        assertNotNull("Sketch should not be null", sketch)
        assertEquals("Extension name should be Graphviz", "Graphviz", sketch.getExtensionName())
        assertEquals("View text should match content", validDotContent, sketch.getViewText())
    }
    
    @Test
    fun `should create sketch for invalid DOT content`() {
        val invalidDotContent = "This is not a valid DOT content"
        
        val sketch = provider.create(project, invalidDotContent)
        
        assertNotNull("Sketch should not be null", sketch)
        assertEquals("Extension name should be Graphviz", "Graphviz", sketch.getExtensionName())
        assertEquals("View text should match content", invalidDotContent, sketch.getViewText())
    }
    
    @Test
    fun `should create sketch for empty content`() {
        val emptyContent = ""
        
        val sketch = provider.create(project, emptyContent)
        
        assertNotNull("Sketch should not be null", sketch)
        assertEquals("Extension name should be Graphviz", "Graphviz", sketch.getExtensionName())
        assertEquals("View text should match content", emptyContent, sketch.getViewText())
    }
    
    @Test
    fun `should handle complex DOT content`() {
        val complexDotContent = """
            digraph G {
                rankdir=LR;
                node [shape=box];
                
                A [label="Start"];
                B [label="Process"];
                C [label="Decision", shape=diamond];
                D [label="End"];
                
                A -> B;
                B -> C;
                C -> D [label="Yes"];
                C -> B [label="No"];
            }
        """.trimIndent()
        
        val sketch = provider.create(project, complexDotContent)
        
        assertNotNull("Sketch should not be null", sketch)
        assertEquals("Extension name should be Graphviz", "Graphviz", sketch.getExtensionName())
        assertEquals("View text should match content", complexDotContent, sketch.getViewText())
    }
}
