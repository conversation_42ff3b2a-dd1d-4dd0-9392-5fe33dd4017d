package cc.unitmesh.diagram.graphviz.parser

import cc.unitmesh.diagram.graphviz.model.GraphvizGraph
import org.junit.Test
import org.junit.Assert.*

class GraphvizDotParserTest {
    
    @Test
    fun `should parse simple directed graph`() {
        val dotContent = """
            digraph G {
                A -> B;
                B -> C;
                A -> C;
            }
        """.trimIndent()
        
        val graph = GraphvizDotParser.parse(dotContent)
        
        assertNotNull("Graph should not be null", graph)
        assertEquals("Graph type should be DIGRAPH", GraphvizGraph.GraphType.DIGRAPH, graph!!.type)
        assertTrue("Should have nodes", graph.nodes.isNotEmpty())
        assertTrue("Should have edges", graph.edges.isNotEmpty())
    }
    
    @Test
    fun `should parse simple undirected graph`() {
        val dotContent = """
            graph G {
                A -- B;
                B -- C;
                A -- C;
            }
        """.trimIndent()
        
        val graph = GraphvizDotParser.parse(dotContent)
        
        assertNotNull("Graph should not be null", graph)
        assertEquals("Graph type should be GRAPH", GraphvizGraph.GraphType.GRAPH, graph!!.type)
        assertTrue("Should have nodes", graph.nodes.isNotEmpty())
        assertTrue("Should have edges", graph.edges.isNotEmpty())
    }
    
    @Test
    fun `should parse graph with node attributes`() {
        val dotContent = """
            digraph G {
                A [label="Node A", color="red"];
                B [label="Node B", color="blue"];
                A -> B;
            }
        """.trimIndent()
        
        val graph = GraphvizDotParser.parse(dotContent)
        
        assertNotNull("Graph should not be null", graph)
        assertTrue("Should have at least 2 nodes", graph!!.nodes.size >= 2)
        
        val nodeA = graph.nodes.find { it.id == "A" }
        assertNotNull("Node A should exist", nodeA)
    }
    
    @Test
    fun `should parse graph with edge labels`() {
        val dotContent = """
            digraph G {
                A -> B [label="edge AB"];
                B -> C [label="edge BC"];
            }
        """.trimIndent()
        
        val graph = GraphvizDotParser.parse(dotContent)
        
        assertNotNull("Graph should not be null", graph)
        assertTrue("Should have edges", graph!!.edges.isNotEmpty())
        
        val edgeAB = graph.edges.find { it.from == "A" && it.to == "B" }
        assertNotNull("Edge A->B should exist", edgeAB)
    }
    
    @Test
    fun `should validate valid DOT content`() {
        val validDot = """
            digraph G {
                A -> B;
            }
        """.trimIndent()
        
        assertTrue("Should be valid DOT", GraphvizDotParser.isValidDotContent(validDot))
    }
    
    @Test
    fun `should invalidate invalid DOT content`() {
        val invalidDot = "This is not a valid DOT content"
        
        assertFalse("Should be invalid DOT", GraphvizDotParser.isValidDotContent(invalidDot))
    }
    
    @Test
    fun `should return null for malformed DOT content`() {
        val malformedDot = """
            digraph G {
                A -> B
                // Missing semicolon and closing brace
        """.trimIndent()
        
        val graph = GraphvizDotParser.parse(malformedDot)
        
        assertNull("Graph should be null for malformed content", graph)
    }
    
    @Test
    fun `should handle empty graph`() {
        val emptyDot = """
            digraph G {
            }
        """.trimIndent()
        
        val graph = GraphvizDotParser.parse(emptyDot)
        
        assertNotNull("Graph should not be null", graph)
        assertTrue("Should have no nodes", graph!!.nodes.isEmpty())
        assertTrue("Should have no edges", graph.edges.isEmpty())
    }
}
