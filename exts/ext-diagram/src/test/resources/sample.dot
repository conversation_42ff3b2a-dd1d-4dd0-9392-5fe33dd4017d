digraph SampleGraph {
    rankdir=TB;
    node [shape=box, style=filled, fillcolor=lightblue];
    edge [color=darkblue];
    
    // Define nodes
    A [label="Start", shape=ellipse, fillcolor=lightgreen];
    B [label="Process Data"];
    C [label="Decision", shape=diamond, fillcolor=yellow];
    D [label="Output Result"];
    E [label="Error Handler", fillcolor=lightcoral];
    F [label="End", shape=ellipse, fillcolor=lightgreen];
    
    // Define edges
    A -> B [label="input"];
    B -> C [label="validate"];
    C -> D [label="valid"];
    C -> E [label="invalid"];
    D -> F [label="success"];
    E -> F [label="handled"];
    E -> B [label="retry", style=dashed];
}
